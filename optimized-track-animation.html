<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>优化版轨迹动画</title>
    
    <!-- MapboxGL CSS -->
    <link href="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.css" rel="stylesheet">
    
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: #1a1a1a;
        }

        #map {
            position: absolute;
            top: 0;
            left: 0;
            height: 100%;
            width: 100%;
        }

        #controls {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 20px;
            border-radius: 10px;
            z-index: 100;
            min-width: 250px;
            transition: transform 0.3s ease;
        }

        #controls.hidden {
            transform: translateX(320px);
        }

        .panel-toggle {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 40px;
            height: 40px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            font-size: 18px;
            z-index: 101;
            transition: transform 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .panel-toggle:hover {
            background: rgba(0, 0, 0, 1);
            transform: scale(1.1);
        }

        .panel-toggle.panel-hidden {
            transform: translateX(-320px);
        }

        #controls h3 {
            margin: 0 0 15px 0;
            color: #4CAF50;
            text-align: center;
        }

        .control-group {
            margin-bottom: 15px;
        }

        .control-group button {
            width: 100%;
            padding: 10px;
            margin: 5px 0;
            border: none;
            border-radius: 6px;
            background: #4CAF50;
            color: white;
            cursor: pointer;
            font-weight: bold;
        }

        .control-group button:hover {
            background: #45a049;
        }

        .control-group button:disabled {
            background: #666;
            cursor: not-allowed;
        }

        .control-group label {
            display: block;
            margin-bottom: 8px;
            color: #E0E0E0;
        }

        .control-group input[type="range"] {
            width: 100%;
            margin: 8px 0;
        }

        .value-display {
            background: #4CAF50;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-weight: bold;
            font-family: monospace;
        }

        #info-panel {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.9);
            color: white;
            padding: 15px;
            border-radius: 8px;
            z-index: 100;
            font-family: monospace;
            font-size: 12px;
        }

        #info-panel h4 {
            margin: 0 0 10px 0;
            color: #4CAF50;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: #4CAF50;
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>

<body>
    <div id="controls">
        <h3>🎬 newCamera 3D轨迹动画</h3>

        <div class="control-group">
            <label>动画时长: <span class="value-display" id="durationValue">30</span>秒</label>
            <input type="range" id="durationSlider" min="10" max="120" step="5" value="30">
        </div>

        <div class="control-group">
            <button id="loadGPX">📁 加载GPX</button>
            <button id="startAnimation" disabled>▶️ 开始动画</button>
            <button id="pauseAnimation" disabled>⏸️ 暂停动画</button>
            <button id="resetAnimation" disabled>🔄 重置动画</button>
        </div>

        <div class="control-group">
            <label>动画进度</label>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill"></div>
            </div>
            <div style="text-align: center; margin-top: 5px; font-size: 12px;">
                <span id="currentTime">0.0s</span> / <span id="totalTime">30.0s</span>
            </div>
        </div>

        <div class="control-group">
            <label>快速跳转</label>
            <div style="display: flex; gap: 5px; flex-wrap: wrap;">
                <button onclick="seekTo(0)" disabled class="seek-btn" style="font-size: 10px; padding: 5px;">0%</button>
                <button onclick="seekTo(0.25)" disabled class="seek-btn" style="font-size: 10px; padding: 5px;">25%</button>
                <button onclick="seekTo(0.5)" disabled class="seek-btn" style="font-size: 10px; padding: 5px;">50%</button>
                <button onclick="seekTo(0.75)" disabled class="seek-btn" style="font-size: 10px; padding: 5px;">75%</button>
                <button onclick="seekTo(1)" disabled class="seek-btn" style="font-size: 10px; padding: 5px;">100%</button>
            </div>
        </div>
    </div>

    <div id="info-panel">
        <h4>📍 相机信息</h4>
        <p>状态: <span id="status">等待加载GPX</span></p>
        <p>轨迹点: <span id="totalPoints">0</span></p>
        <p>纬度: <span id="latitude">-</span></p>
        <p>经度: <span id="longitude">-</span></p>
        <p>高度: <span id="altitude">-</span>m</p>
    </div>

    <button class="panel-toggle" id="panelToggle" title="隐藏/显示控制面板">⚙️</button>

    <div id='map'></div>

    <!-- MapboxGL JS -->
    <script src="https://api.mapbox.com/mapbox-gl-js/v3.14.0/mapbox-gl.js"></script>
    
    <!-- 配置文件 -->
    <script src="config.js"></script>
    
    <script type="module">
        // 导入newCamera库
        import { CameraAnimationController, GPXParser } from './newCamera/index.js';

        // 使用配置文件中的 Access Token
        mapboxgl.accessToken = CONFIG.MAPBOX_ACCESS_TOKEN;

        // 全局变量
        let map;
        let trackData = [];
        let controller = null;
        let isInitialized = false;
        let isPanelHidden = false;

        // 颜色配置 - 基于高度的渐变
        const COLOR_CONFIG = {
            HIGH_COLOR: [255, 10, 0],    // 最高点：红色
            LOW_COLOR: [255, 235, 0]   // 最低点：浅棕色
        };

        // 根据高度计算颜色
        function getColorByElevation(elevation) {
            if (minElevation === maxElevation) {
                return `rgb(${COLOR_CONFIG.HIGH_COLOR.join(',')})`;
            }

            // 计算高度比例 (0-1)
            const ratio = (elevation - minElevation) / (maxElevation - minElevation);

            // 线性插值计算RGB值
            const r = Math.round(COLOR_CONFIG.LOW_COLOR[0] + (COLOR_CONFIG.HIGH_COLOR[0] - COLOR_CONFIG.LOW_COLOR[0]) * ratio);
            const g = Math.round(COLOR_CONFIG.LOW_COLOR[1] + (COLOR_CONFIG.HIGH_COLOR[1] - COLOR_CONFIG.LOW_COLOR[1]) * ratio);
            const b = Math.round(COLOR_CONFIG.LOW_COLOR[2] + (COLOR_CONFIG.HIGH_COLOR[2] - COLOR_CONFIG.LOW_COLOR[2]) * ratio);

            return `rgb(${r}, ${g}, ${b})`;
        }

        // 更新状态显示
        function updateStatus(message) {
            document.getElementById('status').textContent = message;
        }

        // 初始化地图
        function initMap() {
            map = new mapboxgl.Map({
                container: 'map',
                style: 'mapbox://styles/mapbox/standard-satellite',
                center: [10.3090512, 43.9034624],
                zoom: 14,
                pitch: 65,
                bearing: 0,
                antialias: true,
                // 优化瓦片缓存设置
                maxTileCacheSize: 1000,
                transformRequest: (url, resourceType) => {
                    if (resourceType === 'Tile') {
                        return {
                            url: url,
                            headers: {
                                'Cache-Control': 'max-age=3600'
                            }
                        };
                    }
                }
            });

            map.on('style.load', () => {
                setupTerrain();
                updateStatus('地图已加载，点击"加载GPX"开始');
            });
        }

        // 设置3D地形
        function setupTerrain() {
            // 使用真实的地形数据
            map.addSource('mapbox-terrain-dem', {
                'type': 'raster-dem',
                'url': 'mapbox://mapbox.mapbox-terrain-dem-v1',
                'tileSize': 512,
                'maxzoom': 15
            });

            map.setTerrain({
                'source': 'mapbox-terrain-dem',
                'exaggeration': 1 // 
            });

            map.addLayer({
                'id': 'sky',
                'type': 'sky',
                'paint': {
                    'sky-type': 'atmosphere',
                    'sky-atmosphere-sun': [0.0, 90.0],
                    'sky-atmosphere-sun-intensity': 15
                }
            });

            console.log('真实3D地形已设置');
        }



        // 加载GPX轨迹数据
        async function loadGPXData() {
            try {
                updateStatus('正在加载GPX数据...');

                // 获取动画时长
                const duration = parseInt(document.getElementById('durationSlider').value) * 1000;

                // 创建控制器
                controller = new CameraAnimationController({
                    duration: duration,
                    onUpdate: (animationState) => {
                        updateCameraData(animationState);
                        updateUI(controller.getState());

                        // 更新地图相机位置
                        if (animationState.lookAt) {
                            const lon = animationState.lookAt.lon;
                            const lat = animationState.lookAt.lat;

                            // 检查坐标有效性
                            if (!isNaN(lon) && !isNaN(lat) &&
                                lon >= -180 && lon <= 180 &&
                                lat >= -90 && lat <= 90) {

                                // 计算动态旋转角度（基于动画进度）
                                const rotationSpeed = 360; // 完整动画旋转360度
                                const dynamicBearing = (animationState.progress * rotationSpeed) % 360;

                                map.easeTo({
                                    center: [lon, lat],
                                    zoom: 16,
                                    pitch: 70,
                                    bearing: dynamicBearing,
                                    duration: 100
                                });
                            } else {
                                console.warn('Invalid coordinates:', { lon, lat });
                            }
                        }
                    },
                    onComplete: () => {
                        updateStatus('动画播放完成');
                        updateUI(controller.getState());

                        // 设置最终相机位置（俯视轨迹）
                        setTimeout(() => {
                            setFinalCameraPosition();
                        }, 500); // 延迟0.5秒后开始最终相机移动
                    }
                });

                // 加载GPX数据
                const result = await controller.initializeFromGPXURL('./workout_track.gpx');

                updateStatus(`成功加载 ${result.pointCount} 个轨迹点`);
                document.getElementById('totalPoints').textContent = result.pointCount;

                // 创建轨迹线
                createTrackLine();

                // 设置地图视图
                fitMapToTrack();

                // 启用控制按钮
                enableControls();

                // 更新UI状态
                updateUI(controller.getState());

                isInitialized = true;

            } catch (error) {
                console.error('加载GPX数据失败:', error);
                updateStatus(`加载失败: ${error.message}`);
            }
        }

        // 启用控制按钮
        function enableControls() {
            document.getElementById('startAnimation').disabled = false;
            document.getElementById('resetAnimation').disabled = false;
            document.querySelectorAll('.seek-btn').forEach(btn => {
                btn.disabled = false;
            });
        }

        // 更新相机数据显示
        function updateCameraData(animationState) {
            if (animationState.lookAt) {
                const lat = animationState.lookAt.lat;
                const lon = animationState.lookAt.lon;
                const alt = animationState.lookAt.alt;

                // 安全显示坐标数据
                document.getElementById('latitude').textContent =
                    (!isNaN(lat) ? lat.toFixed(6) : 'N/A') + '°';
                document.getElementById('longitude').textContent =
                    (!isNaN(lon) ? lon.toFixed(6) : 'N/A') + '°';
                document.getElementById('altitude').textContent =
                    (!isNaN(alt) ? alt.toFixed(1) : 'N/A') + 'm';

                // 更新轨迹绘制
                updateTrackVisualization(animationState.progress);
            }
        }

        // 更新轨迹可视化
        function updateTrackVisualization(progress) {
            if (!window.trackPoints) {
                console.log('No trackPoints available');
                return;
            }

            if (!map.getSource('dynamic-track-line')) {
                console.log('No dynamic-track-line source');
                return;
            }

            // 计算当前应该显示的轨迹点数量
            const totalPoints = window.trackPoints.length;
            const currentPointIndex = Math.floor(progress * totalPoints);

            console.log(`Updating track visualization: progress=${progress.toFixed(3)}, currentPointIndex=${currentPointIndex}/${totalPoints}`);

            // 获取当前应该显示的轨迹点
            const visiblePoints = window.trackPoints.slice(0, Math.max(1, currentPointIndex));
            const coordinates = visiblePoints.map(point => [point.longitude, point.latitude]);

            // 更新动态轨迹线
            map.getSource('dynamic-track-line').setData({
                'type': 'Feature',
                'properties': {},
                'geometry': {
                    'type': 'LineString',
                    'coordinates': coordinates
                }
            });

            // 更新当前位置点
            if (currentPointIndex > 0 && currentPointIndex < totalPoints) {
                const currentPoint = window.trackPoints[currentPointIndex];
                if (map.getSource('current-position')) {
                    map.getSource('current-position').setData({
                        'type': 'Feature',
                        'properties': {},
                        'geometry': {
                            'type': 'Point',
                            'coordinates': [currentPoint.longitude, currentPoint.latitude]
                        }
                    });
                }
            }
        }

        // 更新UI状态
        function updateUI(state) {
            // 更新进度条
            const progress = (state.progress * 100).toFixed(1);
            document.getElementById('progressFill').style.width = `${progress}%`;

            // 更新时间显示
            document.getElementById('currentTime').textContent = `${(state.currentTime / 1000).toFixed(1)}s`;
            document.getElementById('totalTime').textContent = `${(state.duration / 1000).toFixed(1)}s`;

            // 更新按钮状态
            const hasPath = state.hasPath;
            document.getElementById('startAnimation').disabled = !hasPath;
            document.getElementById('pauseAnimation').disabled = !hasPath || !state.isPlaying;
            document.getElementById('resetAnimation').disabled = !hasPath;

            // 更新跳转按钮
            document.querySelectorAll('.seek-btn').forEach(btn => {
                btn.disabled = !hasPath;
            });
        }
        // 清理已存在的轨迹线
        function clearExistingTrackLines() {
            const layersToRemove = ['dynamic-track-line-layer', 'current-position-point'];
            const sourcesToRemove = ['dynamic-track-line', 'current-position'];

            layersToRemove.forEach(layerId => {
                if (map.getLayer(layerId)) {
                    map.removeLayer(layerId);
                }
            });

            sourcesToRemove.forEach(sourceId => {
                if (map.getSource(sourceId)) {
                    map.removeSource(sourceId);
                }
            });
        }

        // 创建轨迹线
        function createTrackLine() {
            console.log('createTrackLine called');
            if (!controller || !controller.cameraPath) {
                console.log('No controller or cameraPath');
                return;
            }

            const trackPoints = controller.cameraPath.trackPoints;
            if (!trackPoints || trackPoints.length === 0) {
                console.log('No trackPoints or empty array');
                return;
            }

            console.log(`Creating track line with ${trackPoints.length} points`);
            console.log('First track point:', trackPoints[0]);
            console.log('Track point keys:', Object.keys(trackPoints[0]));

            // 清理已存在的轨迹线
            clearExistingTrackLines();

            // 存储轨迹点供动画使用
            window.trackPoints = trackPoints;

            // 创建动态轨迹线（初始为空）
            map.addSource('dynamic-track-line', {
                'type': 'geojson',
                'data': {
                    'type': 'Feature',
                    'properties': {},
                    'geometry': {
                        'type': 'LineString',
                        'coordinates': []
                    }
                }
            });

            map.addLayer({
                'id': 'dynamic-track-line-layer',
                'type': 'line',
                'source': 'dynamic-track-line',
                'layout': {
                    'line-join': 'round',
                    'line-cap': 'round'
                },
                'paint': {
                    'line-color': '#FF5722',
                    'line-width': 11,
                    'line-opacity': 0.9
                }
            });

            // 不创建静态轨迹线，只通过动画动态生成

            console.log('Track lines created successfully');

            // 当前位置点
            const firstPoint = trackPoints[0];
            map.addSource('current-position', {
                'type': 'geojson',
                'data': {
                    'type': 'Feature',
                    'properties': {},
                    'geometry': {
                        'type': 'Point',
                        'coordinates': [firstPoint.longitude, firstPoint.latitude]
                    }
                }
            });

            map.addLayer({
                'id': 'current-position-point',
                'type': 'circle',
                'source': 'current-position',
                'paint': {
                    'circle-radius': 8,
                    'circle-color': '#00FF00',
                    'circle-stroke-color': '#FFFFFF',
                    'circle-stroke-width': 3
                }
            });
        }

        // 设置地图视图
        function fitMapToTrack() {
            if (!controller || !controller.cameraPath) return;

            const trackPoints = controller.cameraPath.trackPoints;
            if (!trackPoints || trackPoints.length === 0) return;

            const coordinates = trackPoints.map(point => [point.longitude, point.latitude]);
            const bounds = coordinates.reduce((bounds, coord) => {
                return bounds.extend(coord);
            }, new mapboxgl.LngLatBounds(coordinates[0], coordinates[0]));

            map.fitBounds(bounds, {
                padding: 100,
                pitch: 70,
                bearing: 0
            });
        }

        // 计算轨迹中心点
        function getTrackCenter() {
            if (!controller || !controller.cameraPath || !controller.cameraPath.trackPoints) {
                return null;
            }

            const trackPoints = controller.cameraPath.trackPoints;
            if (trackPoints.length === 0) return null;

            let totalLat = 0;
            let totalLon = 0;

            trackPoints.forEach(point => {
                totalLat += point.latitude;
                totalLon += point.longitude;
            });

            return {
                lat: totalLat / trackPoints.length,
                lon: totalLon / trackPoints.length
            };
        }

        // 设置最终相机位置（俯视轨迹）
        function setFinalCameraPosition() {
            const center = getTrackCenter();
            if (!center) return;

            // 转换高度3000为合适的zoom级别
            // 高度3000米大约对应zoom 12-13
            const finalZoom = 14;

            map.easeTo({
                center: [center.lon, center.lat],
                zoom: finalZoom,
                pitch: 55, // 俯仰角55度
                bearing: 0, // 重置旋转角度
                duration: 2000 // 2秒过渡动画
            });
        }

        // 切换面板显示/隐藏
        function togglePanel() {
            const controls = document.getElementById('controls');
            const toggleBtn = document.getElementById('panelToggle');

            isPanelHidden = !isPanelHidden;

            if (isPanelHidden) {
                controls.classList.add('hidden');
                toggleBtn.classList.add('panel-hidden');
                toggleBtn.innerHTML = '⚙️';
                toggleBtn.title = '显示控制面板';
            } else {
                controls.classList.remove('hidden');
                toggleBtn.classList.remove('panel-hidden');
                toggleBtn.innerHTML = '✖️';
                toggleBtn.title = '隐藏控制面板';
            }
        }

        // 开始动画
        function startAnimation() {
            if (!controller || !isInitialized) return;

            const state = controller.getState();
            if (state.isPlaying) {
                controller.pause();
                updateStatus('动画已暂停');
            } else {
                controller.play();
                updateStatus('动画播放中...');
            }
        }

        // 暂停动画
        function pauseAnimation() {
            if (!controller) return;

            controller.pause();
            updateStatus('动画已暂停');
        }

        // 重置动画
        function resetAnimation() {
            if (!controller) return;

            controller.stop();
            updateStatus('动画已重置');

            // 重置地图视图
            fitMapToTrack();
        }

        // 跳转到指定进度
        function seekTo(progress) {
            if (!controller) return;

            controller.seekToProgress(progress);
            updateStatus(`跳转到 ${(progress * 100).toFixed(0)}%`);
        }

        // 全局函数供HTML调用
        window.loadGPXData = loadGPXData;
        window.startAnimation = startAnimation;
        window.pauseAnimation = pauseAnimation;
        window.resetAnimation = resetAnimation;
        window.seekTo = seekTo;

        // 事件监听器
        function setupEventListeners() {
            // 动画时长控制
            document.getElementById('durationSlider').addEventListener('input', (e) => {
                const duration = parseInt(e.target.value);
                document.getElementById('durationValue').textContent = duration;
                document.getElementById('totalTime').textContent = `${duration}.0s`;
            });

            // 按钮控制
            document.getElementById('loadGPX').addEventListener('click', loadGPXData);
            document.getElementById('startAnimation').addEventListener('click', startAnimation);
            document.getElementById('pauseAnimation').addEventListener('click', pauseAnimation);
            document.getElementById('resetAnimation').addEventListener('click', resetAnimation);

            // 面板切换
            document.getElementById('panelToggle').addEventListener('click', togglePanel);
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', () => {
            initMap();
            setupEventListeners();
            updateStatus('地图初始化中...');
        });
    </script>
</body>

</html>
