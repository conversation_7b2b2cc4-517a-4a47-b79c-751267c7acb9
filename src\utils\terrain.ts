import type { Map as MapboxMap } from 'mapbox-gl'

// 地形数据源配置
export interface TerrainConfig {
  source: string
  exaggeration: number | any[]
}

// 地形方差检测配置
export interface TerrainVarianceConfig {
  enabled: boolean
  imageUrl?: string
  baseExaggeration?: number
  varianceMultiplier?: number
}

/**
 * 地形系统管理器
 * 参照 maps.suunto.com 的智能地形数据源选择和夸张度调整
 */
export class TerrainManager {
  private map: MapboxMap
  private currentSource: string = 'mapbox-dem'
  private elevationImage?: HTMLImageElement
  private elevationContext?: CanvasRenderingContext2D
  private varianceConfig: TerrainVarianceConfig

  constructor(map: MapboxMap, varianceConfig: TerrainVarianceConfig = { enabled: false }) {
    this.map = map
    this.varianceConfig = {
      baseExaggeration: 1.35,
      varianceMultiplier: 0.7,
      ...varianceConfig
    }
  }

  /**
   * 初始化地形系统
   */
  async initialize(): Promise<void> {
    try {
      // 添加 Mapbox DEM 数据源
      if (!this.map.getSource('mapbox-dem')) {
        this.map.addSource('mapbox-dem', {
          type: 'raster-dem',
          url: 'mapbox://mapbox.mapbox-terrain-dem-v1',
          tileSize: 512,
          maxzoom: 14
        })
      }

      // 如果启用地形方差检测，加载地形变化图像
      if (this.varianceConfig.enabled && this.varianceConfig.imageUrl) {
        await this.loadTerrainVarianceImage(this.varianceConfig.imageUrl)
      }

      // 设置初始地形
      this.updateTerrain()

      // 监听地图移动事件，动态更新地形
      this.map.on('moveend', () => this.updateTerrain())
      this.map.on('zoomend', () => this.updateTerrain())

      console.log('地形系统初始化完成')
    } catch (error) {
      console.error('地形系统初始化失败:', error)
      throw error
    }
  }

  /**
   * 加载地形变化检测图像
   */
  private async loadTerrainVarianceImage(imageUrl: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.elevationImage = new Image()
      this.elevationImage.crossOrigin = 'anonymous'
      
      this.elevationImage.onload = () => {
        // 创建 Canvas 用于像素采样
        const canvas = document.createElement('canvas')
        canvas.width = this.elevationImage!.width
        canvas.height = this.elevationImage!.height
        
        this.elevationContext = canvas.getContext('2d')
        if (this.elevationContext) {
          this.elevationContext.drawImage(this.elevationImage!, 0, 0)
          console.log('地形方差图像加载完成')
          resolve()
        } else {
          reject(new Error('无法创建 Canvas 上下文'))
        }
      }
      
      this.elevationImage.onerror = () => {
        reject(new Error(`无法加载地形方差图像: ${imageUrl}`))
      }
      
      this.elevationImage.src = imageUrl
    })
  }

  /**
   * 更新地形渲染
   * 参照原项目的智能地形数据源选择和夸张度计算
   */
  updateTerrain(): void {
    try {
      const center = this.map.getCenter()
      const zoom = this.map.getZoom()
      
      let exaggeration = this.varianceConfig.baseExaggeration || 1.35
      let source = 'mapbox-dem' // 默认使用 Mapbox DEM

      // 基于地形方差调整夸张度
      if (this.varianceConfig.enabled && this.elevationContext && this.elevationImage) {
        const variance = this.calculateTerrainVariance(center.lng, center.lat)
        // 地形变化小的区域使用更大的夸张度
        exaggeration = 1 + (1 - variance) * (this.varianceConfig.varianceMultiplier || 0.7)
      }

      // 创建缩放级别自适应的夸张度配置
      const terrainConfig: TerrainConfig = {
        source,
        exaggeration: [
          'interpolate',
          ['linear'],
          ['zoom'],
          8, exaggeration + 0.2, // 低缩放级别使用更大夸张度
          12, exaggeration,      // 中等缩放级别使用基础夸张度
          16, exaggeration * 0.8 // 高缩放级别使用较小夸张度
        ]
      }

      // 应用地形设置
      this.map.setTerrain(terrainConfig)
      this.currentSource = source

      console.log(`地形更新: source=${source}, exaggeration=${exaggeration.toFixed(2)}, zoom=${zoom.toFixed(1)}`)
    } catch (error) {
      console.error('地形更新失败:', error)
    }
  }

  /**
   * 计算指定位置的地形方差
   * 基于地形变化图像的像素值计算
   */
  private calculateTerrainVariance(lng: number, lat: number): number {
    if (!this.elevationContext || !this.elevationImage) {
      return 0.5 // 默认中等方差
    }

    try {
      // 将地理坐标转换为图像像素坐标
      // 这里使用简单的线性映射，实际项目中可能需要更复杂的投影转换
      const imageWidth = this.elevationImage.width
      const imageHeight = this.elevationImage.height
      
      // 假设图像覆盖全球范围 (-180 to 180, -90 to 90)
      const x = Math.floor(((lng + 180) / 360) * imageWidth)
      const y = Math.floor(((90 - lat) / 180) * imageHeight)
      
      // 确保坐标在有效范围内
      const clampedX = Math.max(0, Math.min(x, imageWidth - 1))
      const clampedY = Math.max(0, Math.min(y, imageHeight - 1))
      
      // 获取像素数据
      const imageData = this.elevationContext.getImageData(clampedX, clampedY, 1, 1)
      const pixelData = imageData.data
      
      // 使用蓝色通道作为地形变化值 (0-255 映射到 0-1)
      const variance = pixelData[2] / 255
      
      return variance
    } catch (error) {
      console.warn('地形方差计算失败:', error)
      return 0.5
    }
  }

  /**
   * 添加天空层
   */
  addSkyLayer(): void {
    try {
      if (!this.map.getLayer('sky')) {
        this.map.addLayer({
          id: 'sky',
          type: 'sky',
          paint: {
            'sky-type': 'atmosphere',
            'sky-atmosphere-sun': [0.0, 0.0],
            'sky-atmosphere-sun-intensity': 15
          }
        })
        console.log('天空层添加完成')
      }
    } catch (error) {
      console.error('天空层添加失败:', error)
    }
  }

  /**
   * 移除天空层
   */
  removeSkyLayer(): void {
    try {
      if (this.map.getLayer('sky')) {
        this.map.removeLayer('sky')
        console.log('天空层移除完成')
      }
    } catch (error) {
      console.error('天空层移除失败:', error)
    }
  }

  /**
   * 设置地形夸张度
   */
  setExaggeration(exaggeration: number): void {
    this.varianceConfig.baseExaggeration = exaggeration
    this.updateTerrain()
  }

  /**
   * 获取当前地形配置
   */
  getCurrentConfig(): { source: string; exaggeration: number } {
    return {
      source: this.currentSource,
      exaggeration: this.varianceConfig.baseExaggeration || 1.35
    }
  }

  /**
   * 销毁地形管理器
   */
  destroy(): void {
    // 移除事件监听器
    this.map.off('moveend', this.updateTerrain)
    this.map.off('zoomend', this.updateTerrain)
    
    // 清理资源
    this.elevationImage = undefined
    this.elevationContext = undefined
    
    console.log('地形管理器已销毁')
  }
}
