<template>
  <div class="terrain-controls">
    <n-card size="small" class="controls-card">
      <template #header>
        <span>🏔️ 地形控制</span>
      </template>

      <n-space vertical size="medium">
        <!-- 地形开关 -->
        <div class="control-item">
          <n-space justify="space-between" align="center">
            <span class="control-label">启用地形</span>
            <n-switch 
              v-model:value="terrainEnabled" 
              @update:value="handleTerrainToggle"
            />
          </n-space>
        </div>

        <!-- 地形夸张度控制 -->
        <div class="control-item" v-if="terrainEnabled">
          <div class="control-label">
            地形夸张度: {{ exaggeration.toFixed(2) }}x
          </div>
          <n-slider
            v-model:value="exaggeration"
            :min="0.5"
            :max="3.0"
            :step="0.1"
            @update:value="handleExaggerationChange"
            :tooltip="false"
          />
        </div>

        <!-- 天空层控制 -->
        <div class="control-item" v-if="terrainEnabled">
          <n-space justify="space-between" align="center">
            <span class="control-label">天空层</span>
            <n-switch 
              v-model:value="skyEnabled" 
              @update:value="handleSkyToggle"
            />
          </n-space>
        </div>

        <!-- 地形信息显示 -->
        <div class="terrain-info" v-if="terrainEnabled && terrainConfig">
          <n-descriptions :column="1" size="small">
            <n-descriptions-item label="数据源">
              {{ terrainConfig.source }}
            </n-descriptions-item>
            <n-descriptions-item label="当前夸张度">
              {{ terrainConfig.exaggeration.toFixed(2) }}x
            </n-descriptions-item>
          </n-descriptions>
        </div>
      </n-space>

      <template #action>
        <n-space>
          <n-button @click="resetTerrain" size="small">
            重置
          </n-button>
          <n-button @click="$emit('close')" size="small" quaternary>
            关闭
          </n-button>
        </n-space>
      </template>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'
import {
  NCard,
  NSpace,
  NIcon,
  NSwitch,
  NSlider,
  NDescriptions,
  NDescriptionsItem,
  NButton
} from 'naive-ui'

import type { TerrainManager } from '@/utils/terrain'

// Props
interface Props {
  terrainManager?: TerrainManager
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  close: []
}>()

// 状态
const terrainEnabled = ref(true)
const skyEnabled = ref(true)
const exaggeration = ref(1.35)
const terrainConfig = ref<{ source: string; exaggeration: number }>()

// 监听地形管理器变化
watch(
  () => props.terrainManager,
  (manager) => {
    if (manager) {
      // 获取当前地形配置
      terrainConfig.value = manager.getCurrentConfig()
      exaggeration.value = terrainConfig.value.exaggeration
    }
  },
  { immediate: true }
)

// 事件处理
const handleTerrainToggle = (enabled: boolean) => {
  if (!props.terrainManager) return

  if (enabled) {
    // 启用地形
    props.terrainManager.updateTerrain()
    console.log('地形已启用')
  } else {
    // 禁用地形
    // 注意：Mapbox GL JS 没有直接禁用地形的方法
    // 这里可以设置夸张度为 0 来模拟禁用效果
    props.terrainManager.setExaggeration(0)
    console.log('地形已禁用')
  }
}

const handleExaggerationChange = (value: number) => {
  if (!props.terrainManager) return
  
  props.terrainManager.setExaggeration(value)
  
  // 更新配置显示
  terrainConfig.value = props.terrainManager.getCurrentConfig()
  
  console.log(`地形夸张度已调整为: ${value.toFixed(2)}x`)
}

const handleSkyToggle = (enabled: boolean) => {
  if (!props.terrainManager) return

  if (enabled) {
    props.terrainManager.addSkyLayer()
    console.log('天空层已启用')
  } else {
    props.terrainManager.removeSkyLayer()
    console.log('天空层已禁用')
  }
}

const resetTerrain = () => {
  terrainEnabled.value = true
  skyEnabled.value = true
  exaggeration.value = 1.35
  
  if (props.terrainManager) {
    props.terrainManager.setExaggeration(1.35)
    props.terrainManager.addSkyLayer()
    terrainConfig.value = props.terrainManager.getCurrentConfig()
  }
  
  console.log('地形设置已重置')
}
</script>

<style scoped>
.terrain-controls {
  user-select: none;
}

.controls-card {
  min-width: 280px;
  backdrop-filter: blur(10px);
  background: rgba(255, 255, 255, 0.95);
}

.controls-card :deep(.n-card__content) {
  padding: 16px;
}

.control-item {
  width: 100%;
}

.control-label {
  font-size: 14px;
  color: var(--n-text-color-1);
  font-weight: 500;
}

.terrain-info {
  padding: 8px;
  background: var(--n-color-2);
  border-radius: 6px;
  font-size: 12px;
}

/* 深色主题适配 */
.dark .controls-card {
  background: rgba(0, 0, 0, 0.85);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .controls-card {
    min-width: 240px;
  }
}
</style>
